import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Logger,
  NotFoundException,
  Param,
  Post,
  Req,
  UseGuards,
  ValidationPipe,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import * as fs from 'fs';
import * as path from 'path';

import { JobsImportService } from '../services/jobs-import.service';
import { JobsProcessingService } from '../services/jobs-processing.service';
import { JobsExportService } from '../services/jobs-export.service';
import { JobsSearchService } from '../services/jobs-search.service';
import { JobsPineconeService } from '../services/jobs-pinecone.service';
import { appLanguages } from '../../shared/enums/languages.enum';
import { PubSubService } from '../../shared/pubsub/pubsub.service';
import { PubSubMessagePattern } from '../../../auth/decorators/pubsub.decorator';
import { PUBSUB_TOPICS } from '../../shared/pubsub/pubsub.constants';
import { JobsImportRepository } from '../repository/jobs-import.repository';
import { mapResponseToClassification } from '../services/mappers/classification.mapper';
import { UserApiService } from 'src/app/user/services/user-api.service';
import { extractTokenFromRequest } from '../../../auth/utils/auth.utils';
import { Request } from 'express';
import { AuthService } from '../../../auth/services/auth.service';
import { KeywordsData, SmartProfileSearchDto } from '../../react-app/dto/smart-profile-search.dto';
import { ClassifyJobDto } from '../../react-app/dto/matched-job.dto';
import { ReactAppService } from '../../react-app/services/react-app.service';
import { ForYouRepository } from '../repository/for-you.repository';
import { JobClassificationDto, JobRatingDto, JobViewDto } from '../../react-app/dto/job-interaction.dto';
import { OpenAIService } from '../../../openai/openai.service';
import { UserIdDto } from '../controllers/dto/user-id.dto';
import { SimplifiedJobSearchResultDto } from '../dtos/simplified-job-search-result.dto';
import { EnrichedJobDataDto } from '../dtos/enriched-job-data.dto';
import { formatJobForResponse } from '../services/helpers/job-data-to-entity.converter';
import { PaginatedSearchDto } from './dto/paginated-search.dto';
import { JobApplicationService } from '../services/job-application.service';
import { getSession, SessionContainer } from 'supertokens-node/recipe/session';
import { AuthGuard } from '../../../auth/guards/auth.guard';
import { TranslateResponse } from '../../translation/decorators/translate-response.decorator';
import { UserActionTrackerService } from '../../user-actions/services/user-action-tracker.service';
import { UserActionSource } from '../../../entities/user-action.entity';

/**
 * Schema for user applications search
 */
class JobUserApplicationsDto extends SmartProfileSearchDto {
  user_id: string;
}

/**
 * Jobs API Controller
 *
 * Handles job-related operations including importing, processing, and exporting jobs
 */
@ApiTags('Jobs')
@Controller('/jobs')
// @UseGuards(AuthGuard)
export class JobsController {
  private readonly logger = new Logger(JobsController.name);

  constructor(
    private readonly pubSubService: PubSubService,
    private readonly importService: JobsImportService,
    private readonly processingService: JobsProcessingService,
    private readonly exportService: JobsExportService,
    private readonly pineconeService: JobsPineconeService,
    private readonly jobsRepository: JobsImportRepository,
    private readonly reactAppService: ReactAppService,
    private readonly searchService: JobsSearchService,
    private readonly userApiService: UserApiService,
    private readonly authService: AuthService,
    private readonly openAIService: OpenAIService,
    private readonly forYouRepository: ForYouRepository,
    private readonly jobApplicationService: JobApplicationService,
    private readonly userActionTracker: UserActionTrackerService,
  ) {}

  /**
   * Simple health check endpoint for the Jobs API
   */
  @Get()
  @ApiOperation({
    summary: 'Health check endpoint',
    description: 'Returns "ok" if the Jobs API is running properly',
  })
  @ApiResponse({
    status: 200,
    description: 'API is healthy',
    schema: {
      type: 'string',
      example: 'ok',
    },
  })
  async status() {
    return 'ok';
  }

  /**
   * @param extId External job ID
   * @param keywordsData Keywords data for enhanced job retrieval
   * @returns Job with the specified external ID
   */
  @Post(':extId/classify')
  @TranslateResponse()
  @ApiOperation({ summary: 'Get job by external ID using keyword matching' })
  @ApiResponse({ status: 200, description: 'Success' })
  @ApiResponse({ status: 404, description: 'Job not found' })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Missing or invalid data',
  })
  async getJob(
    @Param('extId') extId: string,
    @Body() keywordsData: KeywordsData,
  ): Promise<ClassifyJobDto> {
    try {
      // Validate required fields in keywordsData
      if (!extId) {
        throw new BadRequestException('External ID is required');
      }

      if (!keywordsData || !Array.isArray(keywordsData.keywords)) {
        this.logger.warn('Missing or invalid keywords data in request');
        // Provide default values if missing
        keywordsData = {
          keywords: [],
          skill_contexts: [],
        };
      }

      if (!Array.isArray(keywordsData.skill_contexts)) {
        keywordsData.skill_contexts = [];
      }

      const result = await this.reactAppService.classifySingleJob(keywordsData, extId);
      // Create a proper instance of ClassifyJobDto for translation to work
      const classifyJobDto = new ClassifyJobDto();
      classifyJobDto.match = result.match;
      classifyJobDto.mySkills = result.mySkills;
      classifyJobDto.missingSkills = result.missingSkills;
      classifyJobDto.header = result.header;
      classifyJobDto.note = result.note;
      
      return classifyJobDto;
    } catch (error) {
      this.logger.error(
        `Error in job classification for ${extId}: ${error.message}`,
      );

      // Handle specific error types
      if (error instanceof NotFoundException) {
        throw error; // Rethrow not found errors
      }

      // For other errors, return a default response with NO_MATCH
      return {
        match: 'NO_MATCH',
        mySkills: [],
        missingSkills: [],
      };
    }
  }

  /**
   * Enhanced job classification with detailed score breakdown and structured feedback
   */
  @Post('/:extId/classify/enhanced')
  @ApiOperation({
    summary: 'Enhanced job classification with detailed breakdown',
    description: 'Classifies a job match with detailed score breakdown and structured feedback',
  })
  @ApiParam({
    name: 'extId',
    description: 'External ID of the job to classify',
    example: '013c9f4a-54ea-4b65-9e97-1fbf1c4a4606',
  })
  @ApiBody({
    description: 'Keywords data for classification',
    schema: {
      type: 'object',
      required: ['keywords'],
      properties: {
        keywords: {
          type: 'array',
          items: { type: 'string' },
          description: 'User skills and keywords',
          example: ['javascript', 'react', 'frontend development'],
        },
        skill_contexts: {
          type: 'array',
          items: { type: 'string' },
          description: 'Detailed skill contexts',
          example: ['3 years experience with React', 'Frontend development expertise'],
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Enhanced job classification completed successfully',
    type: ClassifyJobDto,
  })
  @ApiResponse({ status: 404, description: 'Job not found' })
  @TranslateResponse()
  async enhancedClassifyJob(
    @Param('extId') extId: string,
    @Body() body: { keywords: string[]; skill_contexts?: string[] },
  ): Promise<ClassifyJobDto> {
    this.logger.log(`Enhanced classification request for job: ${extId}`);

    try {
      // Validate and prepare keywords data
      const keywordsData = {
        keywords: Array.isArray(body.keywords) ? body.keywords : [],
        skill_contexts: Array.isArray(body.skill_contexts) ? body.skill_contexts : [],
      };

      if (keywordsData.keywords.length === 0) {
        this.logger.warn('No keywords provided for enhanced classification');
        return {
          match: 'NO_MATCH',
          mySkills: [],
          missingSkills: [],
        };
      }

      // Get enhanced classification with detailed breakdown
      const result = await this.reactAppService.classifySingleJobEnhanced(keywordsData, extId);

      // Create a proper instance of ClassifyJobDto for translation to work
      const classifyJobDto = new ClassifyJobDto();
      classifyJobDto.match = result.match;
      classifyJobDto.mySkills = result.mySkills;
      classifyJobDto.missingSkills = result.missingSkills;
      classifyJobDto.header = result.header;
      classifyJobDto.note = result.note;
      classifyJobDto.scoreBreakdown = result.scoreBreakdown;
      classifyJobDto.feedback = result.feedback;

      return classifyJobDto;
    } catch (error) {
      this.logger.error(
        `Error in enhanced job classification for ${extId}: ${error.message}`,
      );

      // Handle specific error types
      if (error instanceof NotFoundException) {
        throw error; // Rethrow not found errors
      }

      // For other errors, return a default response with NO_MATCH
      return {
        match: 'NO_MATCH',
        mySkills: [],
        missingSkills: [],
      };
    }
  }

  /**
   * Triggers a job import event via PubSub
   */
  @Post('/import')
  @ApiOperation({
    summary: 'Trigger job import process',
    description:
      'Publishes a message to the PubSub topic to start the job import process',
  })
  @ApiResponse({
    status: 201,
    description: 'Import event successfully triggered',
    schema: {
      type: 'object',
      properties: {
        messageId: {
          type: 'string',
          description: 'ID of the published message',
        },
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Failed to publish message to PubSub',
  })
  async sendImportEvent() {
    const result = await this.pubSubService.publishMessage(
      this.pubSubService.getJobImportTopic(),
      true,
    );

    if (!result.success) {
      this.logger.error(`Failed to publish message: ${result.error}`);
      throw new BadRequestException('Failed to publish message to PubSub');
    }

    return { success: true, messageId: result.messageId };
  }

  /**
   * Manually triggers job processing
   */
  @Post('/processing')
  @ApiOperation({
    summary: 'Trigger job processing',
    description:
      'Manually starts the job processing pipeline for unprocessed jobs',
  })
  @ApiResponse({
    status: 201,
    description: 'Processing successfully started',
    schema: {
      type: 'object',
      properties: {
        processed: { type: 'number', description: 'Number of jobs processed' },
        success: { type: 'boolean', description: 'Processing status' },
      },
    },
  })
  @ApiResponse({ status: 500, description: 'Failed to process jobs' })
  async processing() {
    return await this.processingService.processJobs();
  }
  
  /**
   * Manually triggers Jobly import process
   * DISABLED IN PRODUCTION ENVIRONMENT
   */
  @Post('/jobly-import')
  @ApiOperation({
    summary: 'Trigger Jobly import process',
    description: 'Manually starts the import process for Jobly jobs (disabled in production)',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        languages: {
          type: 'array',
          items: { type: 'string' },
          description: 'Languages to translate job data to (defaults to [\'en\'])',
          example: ['en'],
        },
        limitJobs: {
          type: 'number',
          description: 'Optional limit for number of jobs to import (for testing)',
          example: 20,
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Import successfully started',
    schema: {
      type: 'object',
      properties: {
        processed: { type: 'number', description: 'Number of jobs processed' },
        success: { type: 'boolean', description: 'Import status' },
      },
    },
  })
  @ApiResponse({ status: 403, description: 'Jobly import is disabled in production' })
  @ApiResponse({ status: 500, description: 'Failed to import Jobly jobs' })
  async importJobly(@Body() body?: { languages?: string[]; limitJobs?: number }) {
    // Disable Jobly import in production
    if (process.env.NODE_ENV === 'production') {
      this.logger.warn('Jobly import endpoint called in production environment - rejected');
      throw new BadRequestException('Jobly import is disabled in production environment');
    }
    
    try {
      const languages = body?.languages || ['en'];
      const limitJobs = body?.limitJobs;
      this.logger.log(`Manually triggering Jobly import with languages: ${languages.join(', ')}${limitJobs ? ` (limited to ${limitJobs} jobs)` : ''}`);
      return await this.importService.importFromJobly(languages, limitJobs);
    } catch (error) {
      this.logger.error(`Error importing from Jobly: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to import from Jobly: ${error.message}`);
    }
  }

  /**
   * Clear Jobly import cache to allow new imports
   * DISABLED IN PRODUCTION ENVIRONMENT
   */
  @Post('/jobly-import/clear-cache')
  @ApiOperation({
    summary: 'Clear Jobly import cache',
    description: 'Clears the Jobly import cache lock to allow new import processes (disabled in production)',
  })
  @ApiResponse({
    status: 201,
    description: 'Cache cleared successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', description: 'Operation status' },
        message: { type: 'string', description: 'Status message' },
      },
    },
  })
  @ApiResponse({ status: 403, description: 'Jobly cache operations are disabled in production' })
  async clearJoblyImportCache() {
    // Disable Jobly cache operations in production
    if (process.env.NODE_ENV === 'production') {
      this.logger.warn('Jobly cache clear endpoint called in production environment - rejected');
      throw new BadRequestException('Jobly cache operations are disabled in production environment');
    }
    
    try {
      this.logger.log('Clearing Jobly import cache');
      await this.importService.clearJoblyImportCache();
      return {
        success: true,
        message: 'Jobly import cache cleared successfully',
      };
    } catch (error) {
      this.logger.error(`Error clearing Jobly import cache: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to clear cache: ${error.message}`);
    }
  }

  /**
   * PubSub handler for job import events
   * Not directly accessible via HTTP
   */
  @PubSubMessagePattern(PUBSUB_TOPICS.JOB_IMPORT)
  async import() {
    this.logger.log('Received job import message from PubSub topic');
    return await this.importService.import({ languages: appLanguages });
  }

  /**
   * Exports unprocessed jobs to a CSV file for batch processing
   */
  @Post('/export-batch')
  @ApiOperation({
    summary: 'Export unprocessed jobs to CSV',
    description:
      'Exports unprocessed jobs to a CSV file for batch processing with external tools',
  })
  @ApiBody({
    description: 'Export configuration',
    schema: {
      type: 'object',
      properties: {
        batchSize: {
          type: 'number',
          description: 'Maximum number of jobs to export (default: 1000)',
          example: 500,
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Jobs successfully exported',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        filePath: { type: 'string', example: '/tmp/jobs-export-20250313.csv' },
        message: {
          type: 'string',
          example: 'Exported batch file to /tmp/jobs-export-20250313.csv',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'No jobs available for export',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        message: { type: 'string', example: 'No unprocessed jobs available' },
      },
    },
  })
  async exportBatch(@Body() body?: { batchSize?: number }) {
    try {
      // Default to 1000 if body is undefined or batchSize is not provided
      const batchSize = body?.batchSize || 1000;

      this.logger.log(`Exporting batch of jobs with batchSize: ${batchSize}`);
      const filePath =
        await this.exportService.exportUnprocessedJobsToCSV(batchSize);

      if (!filePath) {
        return { success: false, message: 'No unprocessed jobs available' };
      }

      return {
        success: true,
        filePath,
        message: `Exported batch file to ${filePath}`,
      };
    } catch (error) {
      this.logger.error(
        `Error exporting job batch: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to export job batch: ${error.message}`,
      );
    }
  }

  /**
   * Callback endpoint for the job processor to update job classifications
   */
  @Post('/callback')
  @ApiOperation({
    summary: 'Job classification callback endpoint',
    description:
      'Receives classification results from external job processors and updates job records',
  })
  @ApiBody({
    description: 'Job classification data',
    schema: {
      type: 'object',
      required: ['ext_id', 'result'],
      properties: {
        ext_id: {
          type: 'string',
          description: 'External ID of the job',
          example: 'job-123456',
        },
        result: {
          type: 'object',
          description: 'Classification results from the processor',
          example: {
            skills: ['JavaScript', 'React', 'Node.js'],
            categories: ['Software Development', 'Web Development'],
            seniority: 'Mid-level',
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Job successfully processed',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Successfully processed job: job-123456',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Missing required fields in request body',
  })
  @ApiResponse({ status: 404, description: 'Job not found' })
  @ApiResponse({ status: 500, description: 'Error processing job' })
  async processJobCallback(@Body() data: any) {
    try {
      this.logger.log(
        `[processJobCallback] Received callback request with data: ${JSON.stringify(data, null, 2)}`,
      );

      // Check if data is undefined or null
      if (!data) {
        this.logger.error(
          '[processJobCallback] Callback received with undefined or null data',
        );
        throw new BadRequestException('Request body is missing');
      }

      const { ext_id, result } = data;
      
      // Validate the JSON structure before proceeding
      if (!ext_id || !result) {
        this.logger.error(
          `[processJobCallback] Invalid callback data received: ${JSON.stringify(data, null, 2)}`,
        );
        throw new BadRequestException(
          'Missing required fields: ext_id or result',
        );
      }
      
      // Validate result is proper JSON
      try {
        // Test if result can be properly stringified and parsed as JSON
        const resultStr = typeof result === 'string' ? result : JSON.stringify(result);
        JSON.parse(typeof result === 'string' ? result : resultStr);
      } catch (jsonError) {
        this.logger.error(
          `[processJobCallback] Malformed JSON in result for job ${ext_id}: ${jsonError.message}`,
          jsonError.stack,
        );
        throw new BadRequestException(
          `Failed to process job ${ext_id}: Malformed JSON in result: ${jsonError.message}`
        );
      }

      this.logger.log(
        `[processJobCallback] Extracted ext_id: ${ext_id}, result: ${JSON.stringify(result, null, 2)}`,
      );

      try {
        // Find the job in the database
        this.logger.log(
          `[processJobCallback] Looking up job with ext_id: ${ext_id}`,
        );
        const job = await this.jobsRepository.findByExtId(ext_id);

        if (!job) {
          this.logger.error(
            `[processJobCallback] Job with ID ${ext_id} not found in database`,
          );
          throw new NotFoundException(`Job with ID ${ext_id} not found`);
        }

        this.logger.log(
          `[processJobCallback] Found job: ${ext_id}, title: "${job.title}", processed: ${job.processed}`,
        );

        // Map the result to our classification format
        this.logger.log(
          `[processJobCallback] Mapping response to classification format for job: ${ext_id}`,
        );
        const classification = mapResponseToClassification(data);
        this.logger.log(
          `[processJobCallback] Mapped classification: ${JSON.stringify(classification, null, 2)}`,
        );

        // Simple approach: ensure we have an existing job and use our repository pattern
        // Update the job with the classification data
        this.logger.log(
          `[processJobCallback] Updating job ${ext_id} with classification data`,
        );
        try {
          await this.jobsRepository.upsertProcessedJobs(job, classification);
          this.logger.log(
            `[processJobCallback] Successfully updated job ${ext_id} with classification data`,
          );
        } catch (upsertError) {
          // For the MVP, we'll handle foreign key errors by providing a clear error message
          if (
            upsertError.message &&
            upsertError.message.includes('foreign key constraint')
          ) {
            this.logger.error(
              `[processJobCallback] Foreign key constraint error for job ${ext_id}: ${upsertError.message}`,
            );
            throw new BadRequestException(
              `Failed to update job ${ext_id} due to missing references (industry or occupation). Please ensure all referenced entities exist in the database.`,
            );
          } else {
            this.logger.error(
              `[processJobCallback] Error upserting processed job for ${ext_id}: ${upsertError.message}`,
              upsertError,
            );
            throw new BadRequestException(
              `Failed to update job ${ext_id} with classification data: ${upsertError.message}`,
            );
          }
        }

        // Mark the job as processed
        this.logger.log(
          `[processJobCallback] Marking job ${ext_id} as processed`,
        );
        await this.jobsRepository.markJobAsProcessed(ext_id);
        this.logger.log(
          `[processJobCallback] Successfully marked job ${ext_id} as processed`,
        );

        this.logger.log(
          `[processJobCallback] All operations completed successfully for job: ${ext_id}`,
        );

        return {
          success: true,
          message: `Successfully processed job: ${ext_id}`,
        };
      } catch (error) {
        this.logger.error(
          `[processJobCallback] Error processing job callback for ${ext_id}: ${error.message}`,
          error.stack,
        );
        // Add context about what was being attempted when the error occurred
        if (error instanceof NotFoundException) {
          throw error;
        } else {
          throw new BadRequestException(
            `Failed to process job ${ext_id}: ${error.message}`,
          );
        }
      }
    } catch (error) {
      this.logger.error(
        `Error in processJobCallback: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Upload processed jobs to Pinecone for vector search
   */
  @Post('/upload-to-pinecone')
  @ApiOperation({
    summary: 'Upload jobs to Pinecone',
    description:
      'Uploads processed jobs to Pinecone vector database for semantic search',
  })
  @ApiBody({
    description: 'Upload configuration',
    schema: {
      type: 'object',
      properties: {
        batchSize: {
          type: 'number',
          description:
            'Maximum number of jobs to process in each batch (default: 100)',
          example: 100,
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Jobs successfully uploaded to Pinecone',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        count: { type: 'number', example: 42 },
        message: {
          type: 'string',
          example: 'Successfully uploaded 42 jobs to Pinecone',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'No jobs available to upload',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        count: { type: 'number', example: 0 },
        message: {
          type: 'string',
          example: 'No processed jobs available to upload',
        },
      },
    },
  })
  @ApiResponse({ status: 500, description: 'Error uploading jobs to Pinecone' })
  async uploadToPinecone(@Body() body: { batchSize?: number }) {
    try {
      const batchSize = body.batchSize || 100;
      this.logger.log('Preparing to process ', batchSize, ' jobs to Pinecone');
      const count =
        await this.pineconeService.uploadAllProcessedJobs(batchSize);

      if (count === 0) {
        return {
          success: false,
          count: 0,
          message: 'No processed jobs available to upload',
        };
      }

      return {
        success: true,
        count,
        message: `Successfully uploaded ${count} jobs to Pinecone`,
      };
    } catch (error) {
      this.logger.error(
        `Error uploading jobs to Pinecone: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to upload jobs to Pinecone: ${error.message}`,
      );
    }
  }

  /**
   * Update all jobs in Pinecone with keywords field
   */
  @Post('/update-pinecone-keywords')
  @ApiOperation({
    summary: 'Update jobs in Pinecone with keywords',
    description:
      'Updates all jobs in Pinecone to ensure they have the keywords field properly populated',
  })
  @ApiBody({
    description: 'Update configuration',
    schema: {
      type: 'object',
      properties: {
        batchSize: {
          type: 'number',
          description:
            'Maximum number of jobs to process in each batch (default: 100)',
          example: 100,
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Jobs successfully updated in Pinecone',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        count: { type: 'number', example: 42 },
        message: {
          type: 'string',
          example: 'Successfully updated 42 jobs in Pinecone with keywords',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'No jobs available to update',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: false },
        count: { type: 'number', example: 0 },
        message: {
          type: 'string',
          example: 'No processed jobs available to update',
        },
      },
    },
  })
  @ApiResponse({ status: 500, description: 'Error updating jobs in Pinecone' })
  async updatePineconeKeywords(@Body() body: { batchSize?: number }) {
    try {
      const batchSize = body.batchSize || 100;
      this.logger.log(
        `Preparing to update ${batchSize} jobs in Pinecone with keywords`,
      );
      const count =
        await this.pineconeService.updateAllJobsWithKeywords(batchSize);

      if (count === 0) {
        return {
          success: false,
          count: 0,
          message: 'No processed jobs available to update with keywords',
        };
      }

      return {
        success: true,
        count,
        message: `Successfully updated ${count} jobs in Pinecone with keywords`,
      };
    } catch (error) {
      this.logger.error(
        `Error updating jobs in Pinecone with keywords: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to update jobs in Pinecone with keywords: ${error.message}`,
      );
    }
  }

  /**
   * Manually triggers Pinecone update to convert expires_at to Unix timestamp
   */
  @Post('/pinecone/update-expires-at')
  @ApiOperation({
    summary: 'Update expires_at field in Pinecone',
    description:
      'Converts all expires_at fields in Pinecone from ISO string to Unix timestamp for better filtering',
  })
  @ApiResponse({
    status: 201,
    description: 'Update process successfully started',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        jobsUpdated: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Failed to update expires_at fields in Pinecone',
  })
  async updateExpiresAtInPinecone() {
    try {
      this.logger.log(
        'Starting migration of expires_at field in Pinecone to Unix timestamp',
      );

      const { updated, failed } = await this.pineconeService.updateExpiresAtToUnixTimestamp();

      return {
        success: true,
        updated,
        failed,
      };
    } catch (error) {
      this.logger.error(
        `Failed to update expires_at fields in Pinecone: ${error.message}`,
      );
      throw new BadRequestException(
        'Failed to update expires_at fields in Pinecone',
      );
    }
  }

  /**
   * Search for jobs by smart profile and filters
   * @param searchParams Search parameters including smart profile and filters from ProcessedJob
   * @returns Matching job titles
   */
  @Post('/search')
  @ApiOperation({
    summary: 'Search for jobs',
    description:
      'Search for jobs using smart profile and filters from ProcessedJob entity with pagination support',
  })
  @ApiBody({
    description: 'Search parameters',
    type: PaginatedSearchDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved jobs',
  })
  // @UseGuards(AuthGuard) // Removed AuthGuard
  @TranslateResponse()
  async searchJobs(
    @Req() req: Request,
    @Body(
      new ValidationPipe({
        forbidNonWhitelisted: false,
        whitelist: false,
        transform: true,
        transformOptions: { enableImplicitConversion: true },
      }),
    )
    searchParams: PaginatedSearchDto,
  ) {
    this.logger.log(
      `Performing job search with params: ${JSON.stringify(searchParams)}`,
    );

    try {
      // Extract userId for caching and tracking from SuperTokens session
      let userId = searchParams.user_id || 'anonymous';
      try {
        // Use the imported getSession function
        const session = await getSession(req, {} as any, { sessionRequired: false }); 
        if (session) {
          userId = searchParams.user_id || session.getUserId();
        }
      } catch (error) {
        // If getSession throws an error (e.g., if Supertokens is down or cookies are malformed),
        // log it but proceed as an anonymous user to not break the search for public users.
        this.logger.warn(`Error fetching SuperTokens session for optional auth: ${error.message}`);
      }
      this.logger.log(`User ID for search: ${userId}`);
      
      // Add userId to search params for caching if not already present
      if (!searchParams.user_id && userId !== 'anonymous') {
        searchParams.user_id = userId;
      }
      
      // Track search action with query parameters if user is authenticated
      if (userId !== 'anonymous') {
        try {
          const searchQuery = searchParams.query || '';
          const filters = { ...searchParams };
          delete filters.query; // Remove query as it's tracked separately
          
          await this.userActionTracker.trackSearch(
            userId,
            searchQuery,
            filters
          );
        } catch (trackingError) {
          // Just log tracking errors, don't disrupt the user experience
          this.logger.warn(`Error tracking search: ${trackingError.message}`);
        }
      }

      // Get user keyword data
      const authHeader = req.headers['authorization'];
      const token = authHeader ? authHeader.replace('Bearer ', '') : undefined;
      
      let keywords: string | undefined = undefined;

      if (searchParams.rerank) {
        const keywordsData = await this.userApiService.getUserKeywordsData(
          token!,
        );
        this.logger.log(`User keywords: ${JSON.stringify(keywordsData)}`);

        // Possibly use the keywords data to enhance the search results
        if (keywordsData && keywordsData.data) {
          keywords = searchParams.query ? searchParams.query + ', ' : '';
          keywords += (keywordsData.data.keywords || []).join(' OR ');
        }
      }

      // Call the search service with pagination parameters
      const result = await this.searchService.searchJobs(
        {
          ...searchParams,
          useCache: searchParams.useCache !== false, // Default to true if not specified
        },
        keywords,
      );

      // Safety check: verify we have jobs before returning
      if (result.jobs.length === 0 && result.meta.total > 0) {
        this.logger.warn(`Got empty jobs array but meta shows ${result.meta.total} total results. This might be a pagination issue.`);
        
        // Add context information to help debug the issue
        this.logger.debug(`Search context: ${JSON.stringify({
          page: searchParams.page,
          limit: searchParams.limit,
          offset: searchParams.offset,
          userId: userId,
          useCache: searchParams.useCache
        })}`);
        
        // If this is an empty result with inconsistent total, let's set the meta.total to 0
        // to ensure consistent response
        result.meta.total = 0;
        result.meta.has_next = false;
      }

      return SimplifiedJobSearchResultDto.from(result);
    } catch (error) {
      this.logger.error(`Error searching jobs: ${error.message}`, error.stack);
      // Return basic result if error occurs with keywords
      return {
        meta: { has_next: false, limit: 0, page: 0, total: 0 },
        jobs: []
      };
    }
  }

  /**
   * Get top jobs with highest match scores for the user
   */
  @Post('/top')
  @ApiOperation({
    summary: 'Get top matching jobs',
    description: 'Retrieves jobs with the highest match scores for the user',
  })
  @ApiBody({
    description: 'Search parameters with user profile',
    type: SmartProfileSearchDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Top jobs retrieved successfully',
  })
  async getTopJobs(
    @Req() req: Request,
    @Body(
      new ValidationPipe({
        forbidNonWhitelisted: false,
        whitelist: false,
      }),
    )
    searchParams: SmartProfileSearchDto,
  ) {
    // Log incoming parameters
    this.logger.log(
      `Received top jobs request with params: ${JSON.stringify(searchParams)}`,
    );

    try {
      // Get user keyword data
      const token = extractTokenFromRequest(req);
      this.logger.log(`Auth token extracted: ${token ? 'found' : 'not found'}`);

      if (!token) {
        this.logger.warn('No authorization token found in request');
        // Return a default result if no token is available
        return {
          success: true,
          data: [],
          message: 'No authorization token provided. Returning empty results.',
        };
      }

      const keywordsData = (
        await this.userApiService.getUserKeywordsData(token)
      ).data.keywords.join(' OR ');

      this.logger.log(`User keywords: ${JSON.stringify(keywordsData)}`);

      searchParams.smart_profile = keywordsData;

      const result = await this.searchService.getTopJobs(searchParams);

      return result;
    } catch (error) {
      this.logger.error(`Error searching jobs: ${error.message}`, error.stack);
      // Return basic result if error occurs with keywords
      return {
        jobs: [],
      };
    }
  }

  /**
   * Get jobs the user has applied to
   */
  @Post('/applications')
  @ApiOperation({
    summary: 'Get user job applications',
    description: 'Retrieves jobs that the user has applied to',
  })
  @ApiBody({
    description: 'Search parameters with user ID',
    type: JobUserApplicationsDto,
  })
  @ApiResponse({
    status: 200,
    description: 'User applications retrieved successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Missing user_id parameter',
  })
  async getUserApplications(@Body() searchParams: JobUserApplicationsDto) {
    if (!searchParams.user_id) {
      throw new BadRequestException(
        'user_id is required for applications search',
      );
    }

    this.logger.log(
      `Received user applications request for user: ${searchParams.user_id}`,
    );
    const result = await this.searchService.getUserApplications(searchParams);

    // Ensure skill_contexts is present in each job
    if (result.jobs.length > 0) {
      result.jobs.forEach((job) => {
        if (!job.skill_contexts) {
          job.skill_contexts = [];
        }
      });
    }

    return result;
  }

  /**
   * Update expired status for jobs
   * Marks jobs as expired if current date > expires_at
   */
  @Post('/update-expired')
  @ApiOperation({
    summary: 'Update expired status for jobs',
    description: 'Marks jobs as expired if their expiration date has passed',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully updated expired status',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        newly_expired_count: { type: 'number', example: 15 },
        total_expired_count: { type: 'number', example: 50 },
        total_unexpired_count: { type: 'number', example: 150 },
        total_unexpired_in_pinecone: { type: 'number', example: 120 },
        message: {
          type: 'string',
          example:
            'Successfully marked 15 jobs as expired. Total expired: 50, Total unexpired: 150, Unexpired in Pinecone: 120',
        },
      },
    },
  })
  async updateExpiredJobs() {
    try {
      // Update newly expired jobs
      const count = await this.jobsRepository.updateExpiredJobs();

      // Get total count of expired jobs
      const totalExpiredCount = await this.jobsRepository.countExpiredJobs();

      // Get total count of unexpired jobs
      const totalUnexpiredCount = await this.jobsRepository.countUnexpiredJobs();

      // Get total count of unexpired jobs uploaded to Pinecone
      const totalUnexpiredInPinecone = await this.jobsRepository.countUnexpiredJobsInPinecone();

      return {
        success: true,
        newly_expired_count: count,
        total_expired_count: totalExpiredCount,
        total_unexpired_count: totalUnexpiredCount,
        total_unexpired_in_pinecone: totalUnexpiredInPinecone,
        message: `Successfully marked ${count} jobs as expired. Total expired: ${totalExpiredCount}, Total unexpired: ${totalUnexpiredCount}, Unexpired in Pinecone: ${totalUnexpiredInPinecone}`,
      };
    } catch (error) {
      this.logger.error(
        `Error updating expired jobs: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to update expired jobs: ${error.message}`,
      );
    }
  }

  /**
   * Get "For You" job recommendations for a user
   */
  @Post('/for-you')
  @ApiOperation({
    summary: 'Get "For You" job recommendations',
    description:
      'Retrieves persisted personalized job recommendations for a specific user',
  })
  @ApiBody({
    description: 'User ID for which to retrieve recommendations',
    type: UserIdDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Recommendations retrieved successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Missing user ID or invalid request',
  })
  async getForYouRecommendations(
    @Body(
      new ValidationPipe({
        forbidNonWhitelisted: false,
        whitelist: false,
      }),
    )
    requestDto: UserIdDto,
  ) {
    if (!requestDto.userId) {
      throw new BadRequestException('userId is required');
    }

    this.logger.log(
      `Getting "For You" recommendations for user: ${requestDto.userId}`,
    );

    try {
      const result = await this.searchService.getForYouJobs(requestDto.userId);

      return {
        success: true,
        data: result,
        message: `Retrieved ${result.jobs.length} "For You" recommendations`,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(
        `Error getting "For You" recommendations: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to retrieve recommendations: ${error.message}`,
      );
    }
  }

  /**
   * Records a job as viewed by a user
   */
  @Post('/view')
  @ApiOperation({
    summary: 'Mark a job as viewed',
    description: 'Records that a user has viewed a specific job',
  })
  @ApiBody({
    description: 'Job view data',
    type: JobViewDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Job successfully marked as viewed',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Job successfully marked as viewed',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Missing required fields in request body',
  })
  @ApiResponse({ status: 404, description: 'Job recommendation not found' })
  async markJobAsViewed(@Body(new ValidationPipe()) jobViewDto: JobViewDto) {
    try {
      const { userId, extId } = jobViewDto;
      await this.forYouRepository.markJobAsViewed(userId, extId);

      return {
        success: true,
        message: 'Job successfully marked as viewed',
      };
    } catch (error) {
      this.logger.error(
        `Error marking job as viewed: ${error.message}`,
        error.stack,
      );

      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new BadRequestException(
        `Failed to mark job as viewed: ${error.message}`,
      );
    }
  }

  /**
   * Updates a job rating (like/dislike) for a user
   */
  @Post('/rate')
  @ApiOperation({
    summary: 'Rate a job',
    description: "Records a user's rating (like/dislike) for a specific job",
  })
  @ApiBody({
    description: 'Job rating data',
    type: JobRatingDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Job rating successfully updated',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Job rating successfully updated' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Missing required fields in request body',
  })
  @ApiResponse({ status: 404, description: 'Job recommendation not found' })
  async rateJob(@Body(new ValidationPipe()) jobRatingDto: JobRatingDto) {
    try {
      const { userId, extId, isLiked } = jobRatingDto;
      await this.forYouRepository.updateJobRating(userId, extId, isLiked);

      return {
        success: true,
        message: 'Job rating successfully updated',
      };
    } catch (error) {
      this.logger.error(
        `Error updating job rating: ${error.message}`,
        error.stack,
      );

      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new BadRequestException(
        `Failed to update job rating: ${error.message}`,
      );
    }
  }

  /**
   * Stores classification data for a job
   */
  @Post('/classification')
  @ApiOperation({
    summary: 'Store job classification data',
    description: 'Stores AI classification data for a specific job and user',
  })
  @ApiBody({
    description: 'Job classification data',
    type: JobClassificationDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Classification data successfully stored',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Classification data successfully stored',
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Missing required fields in request body',
  })
  @ApiResponse({ status: 404, description: 'Job recommendation not found' })
  async storeJobClassification(
    @Body(new ValidationPipe()) jobClassificationDto: JobClassificationDto,
  ) {
    try {
      const { userId, extId, classificationData } = jobClassificationDto;
      await this.forYouRepository.storeClassificationData(
        userId,
        extId,
        classificationData,
      );

      return {
        success: true,
        message: 'Classification data successfully stored',
      };
    } catch (error) {
      this.logger.error(
        `Error storing classification data: ${error.message}`,
        error.stack,
      );

      if (error instanceof NotFoundException) {
        throw error;
      }

      throw new BadRequestException(
        `Failed to store classification data: ${error.message}`,
      );
    }
  }

  /**
   * Test endpoint for generating embeddings and writing them to a file
   * @param text The text to generate an embedding for
   * @returns Information about the generated embedding
   */
  @Post('/embedding')
  @ApiOperation({
    summary: 'Generate embedding for testing',
    description:
      'Generates an embedding for the provided text and writes it to a file',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        text: {
          type: 'string',
          example: 'This is a test text for embedding generation',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Embedding generated successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Embedding generated and written to file',
        },
        dimensions: {
          type: 'number',
          example: 3072,
        },
        filePath: {
          type: 'string',
          example: '/path/to/emb.txt',
        },
      },
    },
  })
  async generateEmbedding(@Body() body: { text: string }) {
    try {
      const { text } = body;

      if (!text) {
        throw new BadRequestException(
          'Text is required for embedding generation',
        );
      }

      this.logger.log(
        `Generating embedding for text: ${text.substring(0, 50)}...`,
      );

      // Generate the embedding using OpenAI service
      const embedding = await this.openAIService.createQueryEmbedding(text);

      // Create a file path in the root directory
      const filePath = path.join(process.cwd(), 'emb.txt');

      // Write the embedding to the file
      fs.writeFileSync(filePath, JSON.stringify(embedding, null, 2));

      this.logger.log(`Embedding written to file: ${filePath}`);

      return {
        message: 'Embedding generated and written to file',
        dimensions: embedding.length,
        filePath,
      };
    } catch (error) {
      this.logger.error(`Error generating embedding: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get a single job by its external ID
   * @param extId External job ID
   * @returns Full job details including all translatable fields
   */
  @Get('/:extId')
  @ApiOperation({ summary: 'Get a single job by external ID' })
  @ApiResponse({ status: 200, description: 'Job found' })
  @ApiResponse({ status: 404, description: 'Job not found' })
  @UseGuards(AuthGuard)
  @TranslateResponse()
  async getJobByExtId(@Param('extId') extId: string, @Req() req: Request): Promise<EnrichedJobDataDto> {
    this.logger.log(`Getting job with ext_id: ${extId}`);

    try {
      // Find job in database
      const job = await this.jobsRepository.findByExtId(extId);
      
      if (!job) {
        throw new NotFoundException(`Job with ID ${extId} not found`);
      }

      // Format job for response with all details
      const formattedJob = formatJobForResponse(job);

      const jobDto = EnrichedJobDataDto.from(formattedJob);
      
      // Track this job view action
      const userId = req['session']?.getUserId();
      if (userId) {
        try {
          // Track job view with the userId if available
          await this.userActionTracker.trackJobView(
            userId,
            extId,
            UserActionSource.BROWSE
          );
        } catch (trackingError) {
          // Just log tracking errors, don't disrupt the user experience
          this.logger.warn(`Error tracking job view: ${trackingError.message}`);
        }
      }
      
      // Get user ID from session if available
      const session = req['session'] as SessionContainer;
      if (session) {
        try {
          const userId = session.getUserId();
          // Check if user has applied to this job
          jobDto.hasApplied = await this.jobApplicationService.hasUserAppliedToJob(userId, extId);
        } catch (err) {
          this.logger.warn(`Error getting application status: ${err.message}`);
          // If there's an error, default to false
          jobDto.hasApplied = false;
        }
      } else {
        // Not authenticated or no session
        jobDto.hasApplied = false;
      }

      return jobDto;
    } catch (error) {
      this.logger.error(`Error getting job ${extId}: ${error.message}`, error.stack);
      
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      throw new BadRequestException(`Failed to get job: ${error.message}`);
    }
  }
}
